import { Component, Input, ElementRef, ViewChild, AfterViewInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { cn } from '../../lib/utils';

@Component({
  selector: 'app-scroll-area',
  standalone: true,
  imports: [CommonModule],
  template: `
    <div [class]="computedClasses" #scrollContainer>
      <div class="h-full w-full rounded-[inherit]" [attr.data-radix-scroll-area-viewport]="">
        <ng-content></ng-content>
      </div>
    </div>
  `,
})
export class ScrollAreaComponent implements AfterViewInit {
  @Input() className = '';
  @ViewChild('scrollContainer', { static: true }) scrollContainer!: ElementRef<HTMLDivElement>;
  
  get computedClasses(): string {
    return cn("relative overflow-hidden", this.className);
  }
  
  ngAfterViewInit(): void {
    // Auto-scroll functionality can be added here if needed
  }
  
  scrollToBottom(): void {
    const viewport = this.scrollContainer.nativeElement.querySelector('[data-radix-scroll-area-viewport]');
    if (viewport) {
      viewport.scrollTop = viewport.scrollHeight;
    }
  }
}
