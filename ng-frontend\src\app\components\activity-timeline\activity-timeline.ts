import { Component, Input, OnInit, signal, effect } from '@angular/core';
import { CommonModule } from '@angular/common';
import { CardComponent, CardHeaderComponent, CardDescriptionComponent, CardContentComponent } from '../ui/card.component';
import { ScrollAreaComponent } from '../ui/scroll-area.component';
import { ProcessedEvent } from '../../models';

@Component({
  selector: 'app-activity-timeline',
  standalone: true,
  imports: [CommonModule, CardComponent, CardHeaderComponent, CardDescriptionComponent, CardContentComponent, ScrollAreaComponent],
  templateUrl: './activity-timeline.html',
  styleUrl: './activity-timeline.scss'
})
export class ActivityTimelineComponent implements OnInit {
  @Input() processedEvents: ProcessedEvent[] = [];
  @Input() isLoading = false;

  isTimelineCollapsed = signal<boolean>(false);

  constructor() {
    // Auto-collapse timeline when loading is complete and events exist
    effect(() => {
      if (!this.isLoading && this.processedEvents.length !== 0) {
        this.isTimelineCollapsed.set(true);
      }
    });
  }

  ngOnInit(): void {
    // Component initialization
  }

  getEventIcon(title: string, index: number): string {
    if (index === 0 && this.isLoading && this.processedEvents.length === 0) {
      return 'loader';
    }
    if (title.toLowerCase().includes('generating')) {
      return 'text-search';
    } else if (title.toLowerCase().includes('thinking')) {
      return 'loader';
    } else if (title.toLowerCase().includes('reflection')) {
      return 'brain';
    } else if (title.toLowerCase().includes('research')) {
      return 'search';
    } else if (title.toLowerCase().includes('finalizing')) {
      return 'pen';
    }
    return 'activity';
  }

  toggleTimeline(): void {
    this.isTimelineCollapsed.update(collapsed => !collapsed);
  }

  formatEventData(data: any): string {
    if (typeof data === 'string') {
      return data;
    } else if (Array.isArray(data)) {
      return (data as string[]).join(', ');
    } else {
      return JSON.stringify(data);
    }
  }
}
