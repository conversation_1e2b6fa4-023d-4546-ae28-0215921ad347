import { Component, input, signal, effect } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MatCardModule } from '@angular/material/card';
import { MatIconModule } from '@angular/material/icon';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { ProcessedEvent } from '../../models';

@Component({
  selector: 'app-activity-timeline',
  imports: [
    CommonModule,
    MatCardModule,
    MatIconModule,
    MatProgressSpinnerModule
  ],
  templateUrl: './activity-timeline.html',
  styleUrl: './activity-timeline.scss'
})
export class ActivityTimeline {
  // Inputs using new signal-based input API
  processedEvents = input<ProcessedEvent[]>([]);
  isLoading = input<boolean>(false);

  // Internal state
  isCollapsed = signal<boolean>(false);

  constructor() {
    // Auto-collapse when loading finishes and events exist
    effect(() => {
      if (!this.isLoading() && this.processedEvents().length > 0) {
        this.isCollapsed.set(true);
      }
    });
  }

  toggleCollapse(): void {
    this.isCollapsed.set(!this.isCollapsed());
  }

  getEventIcon(title: string, index: number): string {
    if (index === 0 && this.isLoading() && this.processedEvents().length === 0) {
      return 'sync';
    }

    const lowerTitle = title.toLowerCase();
    if (lowerTitle.includes('generating')) {
      return 'text_snippet';
    } else if (lowerTitle.includes('thinking')) {
      return 'sync';
    } else if (lowerTitle.includes('reflection')) {
      return 'psychology';
    } else if (lowerTitle.includes('research')) {
      return 'search';
    } else if (lowerTitle.includes('finalizing')) {
      return 'edit';
    }
    return 'timeline';
  }

  shouldShowSpinner(title: string, index: number): boolean {
    const lowerTitle = title.toLowerCase();
    return (index === 0 && this.isLoading() && this.processedEvents().length === 0) ||
           lowerTitle.includes('thinking');
  }

  formatEventData(data: any): string {
    if (typeof data === 'string') {
      return data;
    } else if (Array.isArray(data)) {
      return data.join(', ');
    } else {
      return JSON.stringify(data);
    }
  }

  trackByIndex(index: number, item: ProcessedEvent): number {
    return index;
  }
}
