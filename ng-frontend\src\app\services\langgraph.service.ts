import { Injectable, signal, computed } from '@angular/core';
import { Client } from '@langchain/langgraph-sdk';
import { BehaviorSubject, Subject } from 'rxjs';
import { Message, ProcessedEvent, StreamConfig, StreamEvent } from '../models';

@Injectable({
  providedIn: 'root'
})
export class LangGraphService {
  private client: Client;
  private threadId: string | null = null;

  // Signals for reactive state management
  public messages = signal<Message[]>([]);
  public isLoading = signal<boolean>(false);
  public processedEvents = signal<ProcessedEvent[]>([]);
  public historicalActivities = signal<Record<string, ProcessedEvent[]>>({});

  // Subjects for event handling
  private eventSubject = new Subject<StreamEvent>();
  private finishSubject = new Subject<any>();

  constructor() {
    // Initialize the LangGraph client
    const apiUrl = this.getApiUrl();
    this.client = new Client({ apiUrl });
  }

  private getApiUrl(): string {
    // Use environment detection similar to React version
    return window.location.hostname === 'localhost' 
      ? 'http://localhost:2024' 
      : 'http://localhost:8123';
  }

  async submitMessage(config: StreamConfig): Promise<void> {
    this.isLoading.set(true);
    this.processedEvents.set([]);

    try {
      // Create or get thread
      if (!this.threadId) {
        const thread = await this.client.threads.create();
        this.threadId = thread.thread_id;
      }

      // Update messages immediately
      this.messages.set(config.messages);

      // Start streaming
      const stream = this.client.runs.stream(
        this.threadId,
        'agent',
        {
          input: {
            messages: config.messages,
            initial_search_query_count: config.initial_search_query_count,
            max_research_loops: config.max_research_loops,
            reasoning_model: config.reasoning_model
          }
        }
      );

      // Process stream events
      for await (const event of stream) {
        this.handleStreamEvent(event);
      }

    } catch (error) {
      console.error('Error in stream:', error);
    } finally {
      this.isLoading.set(false);
    }
  }

  private handleStreamEvent(event: any): void {
    // Handle different event types similar to React implementation
    let processedEvent: ProcessedEvent | null = null;

    if (event.event === 'on_chat_model_stream' && event.data?.chunk?.content) {
      // Handle streaming message content
      const currentMessages = this.messages();
      const lastMessage = currentMessages[currentMessages.length - 1];
      
      if (lastMessage && lastMessage.type === 'ai') {
        // Update existing AI message
        const updatedMessages = [...currentMessages];
        updatedMessages[updatedMessages.length - 1] = {
          ...lastMessage,
          content: (lastMessage.content || '') + event.data.chunk.content
        };
        this.messages.set(updatedMessages);
      } else {
        // Create new AI message
        const newMessage: Message = {
          id: Date.now().toString(),
          type: 'ai',
          content: event.data.chunk.content
        };
        this.messages.set([...currentMessages, newMessage]);
      }
    }

    // Handle custom events for activity timeline
    if (event.data) {
      const eventData = event.data;
      
      if (eventData.generate_query) {
        processedEvent = {
          title: "Generating Search Queries",
          data: eventData.generate_query.query_list.join(", ")
        };
      } else if (eventData.web_research) {
        const sources = eventData.web_research.sources_gathered || [];
        const numSources = sources.length;
        const uniqueLabels = [
          ...new Set(sources.map((s: any) => s.label).filter(Boolean))
        ];
        const exampleLabels = uniqueLabels.slice(0, 3).join(", ");
        processedEvent = {
          title: "Web Research",
          data: `Gathered ${numSources} sources. Related to: ${exampleLabels || "N/A"}.`
        };
      } else if (eventData.reflection) {
        processedEvent = {
          title: "Reflection",
          data: eventData.reflection.is_sufficient
            ? "Search successful, generating final answer."
            : `Need more information, searching for ${eventData.reflection.follow_up_queries.join(", ")}`
        };
      } else if (eventData.finalize_answer) {
        processedEvent = {
          title: "Finalizing Answer",
          data: "Composing and presenting the final answer."
        };
        
        // Store historical activities
        const currentMessages = this.messages();
        const lastMessage = currentMessages[currentMessages.length - 1];
        if (lastMessage && lastMessage.type === 'ai' && lastMessage.id) {
          const currentHistorical = this.historicalActivities();
          this.historicalActivities.set({
            ...currentHistorical,
            [lastMessage.id]: [...this.processedEvents()]
          });
        }
      }

      if (processedEvent) {
        const currentEvents = this.processedEvents();
        this.processedEvents.set([...currentEvents, processedEvent]);
      }
    }
  }

  stop(): void {
    // Stop the current stream and reset state
    this.isLoading.set(false);
    // In a real implementation, you'd cancel the stream here
  }

  reset(): void {
    this.messages.set([]);
    this.processedEvents.set([]);
    this.historicalActivities.set({});
    this.isLoading.set(false);
    this.threadId = null;
  }
}
