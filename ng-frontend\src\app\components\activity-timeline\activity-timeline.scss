.timeline-card {
  background-color: #404040;
  border: none;
  border-radius: 0.5rem;
  max-height: 24rem;

  ::ng-deep {
    .mat-mdc-card-header {
      padding: 1rem 1rem 0 1rem;
    }

    .mat-mdc-card-content {
      padding: 0 1rem 1rem 1rem;
      max-height: 20rem;
      overflow-y: auto;
    }
  }
}

.timeline-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  cursor: pointer;
  gap: 0.5rem;
}

.timeline-title {
  font-size: 0.875rem;
  color: #f5f5f5;
  font-weight: 500;
}

.collapse-icon {
  color: #f5f5f5;
  font-size: 1rem;
  width: 1rem;
  height: 1rem;
}

.timeline-content {
  padding-top: 1rem;
}

.timeline-events {
  display: flex;
  flex-direction: column;
  gap: 0;
}

.timeline-item {
  position: relative;
  padding-left: 2rem;
  padding-bottom: 1rem;

  &:last-child {
    padding-bottom: 0;
  }
}

.timeline-line {
  position: absolute;
  left: 0.75rem;
  top: 0.875rem;
  height: 100%;
  width: 0.125rem;
  background-color: #525252;
}

.timeline-icon-container {
  position: absolute;
  left: 0.125rem;
  top: 0.5rem;
  width: 1.5rem;
  height: 1.5rem;
  border-radius: 50%;
  background-color: #525252;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 0.25rem solid #404040;
}

.timeline-icon {
  color: #a3a3a3;
  font-size: 1rem;
  width: 1rem;
  height: 1rem;
}

.timeline-spinner {
  ::ng-deep {
    circle {
      stroke: #a3a3a3;
    }
  }
}

.timeline-spinner-small {
  ::ng-deep {
    circle {
      stroke: #a3a3a3;
    }
  }
}

.timeline-content-item {
  margin-left: 0.5rem;
}

.timeline-item-title {
  font-size: 0.875rem;
  color: #e5e5e5;
  font-weight: 500;
  margin: 0 0 0.125rem 0;
}

.timeline-item-data {
  font-size: 0.75rem;
  color: #d4d4d4;
  line-height: 1.5;
  margin: 0;
}

.no-activity {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 2.5rem 0;
  text-align: center;
}

.no-activity-icon {
  color: #737373;
  font-size: 1.5rem;
  width: 1.5rem;
  height: 1.5rem;
  margin-bottom: 0.75rem;
}

.no-activity-title {
  font-size: 0.875rem;
  color: #737373;
  margin: 0 0 0.25rem 0;
}

.no-activity-subtitle {
  font-size: 0.75rem;
  color: #525252;
  margin: 0;
}
