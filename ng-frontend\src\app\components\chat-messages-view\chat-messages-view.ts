import { Component, Input, Output, EventEmitter, signal, ViewChild, ElementRef, AfterViewChecked } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ScrollAreaComponent } from '../ui/scroll-area.component';
import { ButtonComponent } from '../ui/button.component';
import { InputFormComponent } from '../input-form/input-form';
import { ActivityTimelineComponent } from '../activity-timeline/activity-timeline';
import { Message, ProcessedEvent, EffortLevel, ModelType } from '../../models';
import { marked } from 'marked';

@Component({
  selector: 'app-chat-messages-view',
  standalone: true,
  imports: [CommonModule, ScrollAreaComponent, ButtonComponent, InputFormComponent, ActivityTimelineComponent],
  templateUrl: './chat-messages-view.html',
  styleUrl: './chat-messages-view.scss'
})
export class ChatMessagesViewComponent implements AfterViewChecked {
  @Input() messages: Message[] = [];
  @Input() isLoading = false;
  @Input() liveActivityEvents: ProcessedEvent[] = [];
  @Input() historicalActivities: Record<string, ProcessedEvent[]> = {};

  @Output() submitForm = new EventEmitter<{
    inputValue: string;
    effort: EffortLevel;
    model: ModelType;
  }>();

  @Output() cancel = new EventEmitter<void>();

  @ViewChild('scrollArea') scrollArea!: ScrollAreaComponent;

  copiedMessageId = signal<string | null>(null);

  ngAfterViewChecked(): void {
    // Auto-scroll to bottom when new messages arrive
    if (this.scrollArea) {
      this.scrollArea.scrollToBottom();
    }
  }

  onSubmit(data: { inputValue: string; effort: EffortLevel; model: ModelType }): void {
    this.submitForm.emit(data);
  }

  onCancel(): void {
    this.cancel.emit();
  }

  async handleCopy(text: string, messageId: string): Promise<void> {
    try {
      await navigator.clipboard.writeText(text);
      this.copiedMessageId.set(messageId);
      setTimeout(() => this.copiedMessageId.set(null), 2000);
    } catch (err) {
      console.error('Failed to copy text: ', err);
    }
  }

  renderMarkdown(content: string): string {
    return marked(content) as string;
  }

  getHistoricalActivity(messageId: string): ProcessedEvent[] | undefined {
    return this.historicalActivities[messageId];
  }

  isLastMessage(index: number): boolean {
    return index === this.messages.length - 1;
  }

  shouldShowLiveActivity(index: number): boolean {
    return this.isLastMessage(index) && this.isLoading;
  }

  shouldShowLoadingMessage(): boolean {
    return this.isLoading &&
           (this.messages.length === 0 ||
            this.messages[this.messages.length - 1].type === 'human');
  }
}
