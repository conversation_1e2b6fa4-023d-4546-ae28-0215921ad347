import { Component, input, output, signal, ElementRef, ViewChild, AfterViewChecked } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MatCardModule } from '@angular/material/card';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatChipsModule } from '@angular/material/chips';
import { marked } from 'marked';
import { DomSanitizer, SafeHtml } from '@angular/platform-browser';

import { Message, ProcessedEvent, FormSubmission } from '../../models';
import { InputForm } from '../input-form/input-form';
import { ActivityTimeline } from '../activity-timeline/activity-timeline';

@Component({
  selector: 'app-chat-messages-view',
  imports: [
    CommonModule,
    MatCardModule,
    MatButtonModule,
    MatIconModule,
    MatProgressSpinnerModule,
    MatChipsModule,
    InputForm,
    ActivityTimeline
  ],
  templateUrl: './chat-messages-view.html',
  styleUrl: './chat-messages-view.scss'
})
export class ChatMessagesView implements AfterViewChecked {
  // Inputs using new signal-based input API
  messages = input<Message[]>([]);
  isLoading = input<boolean>(false);
  liveActivityEvents = input<ProcessedEvent[]>([]);
  historicalActivities = input<Record<string, ProcessedEvent[]>>({});

  // Outputs using new signal-based output API
  submitForm = output<FormSubmission>();
  cancel = output<void>();

  // Internal state
  copiedMessageId = signal<string | null>(null);

  @ViewChild('scrollContainer', { static: false }) scrollContainer!: ElementRef;

  constructor(private sanitizer: DomSanitizer) {
    // Configure marked for markdown rendering
    marked.setOptions({
      breaks: true,
      gfm: true
    });
  }

  ngAfterViewChecked(): void {
    this.scrollToBottom();
  }

  private scrollToBottom(): void {
    if (this.scrollContainer) {
      try {
        this.scrollContainer.nativeElement.scrollTop = this.scrollContainer.nativeElement.scrollHeight;
      } catch (err) {
        console.error('Error scrolling to bottom:', err);
      }
    }
  }

  onSubmit(submission: FormSubmission): void {
    this.submitForm.emit(submission);
  }

  onCancel(): void {
    this.cancel.emit();
  }

  async copyMessage(message: Message): Promise<void> {
    try {
      const content = typeof message.content === 'string' ? message.content : JSON.stringify(message.content);
      await navigator.clipboard.writeText(content);
      this.copiedMessageId.set(message.id || '');
      setTimeout(() => this.copiedMessageId.set(null), 2000);
    } catch (err) {
      console.error('Failed to copy text:', err);
    }
  }

  renderMarkdown(content: string): SafeHtml {
    const html = marked(content);
    return this.sanitizer.bypassSecurityTrustHtml(html);
  }

  getMessageContent(message: Message): string {
    return typeof message.content === 'string' ? message.content : JSON.stringify(message.content);
  }

  getHistoricalActivity(messageId: string): ProcessedEvent[] | undefined {
    return this.historicalActivities()[messageId];
  }

  trackByMessageId(index: number, message: Message): string {
    return message.id || `msg-${index}`;
  }
}
