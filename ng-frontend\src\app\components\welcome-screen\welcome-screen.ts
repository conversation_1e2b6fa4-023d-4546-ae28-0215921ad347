import { Component, Input, Output, EventEmitter } from '@angular/core';
import { InputFormComponent } from '../input-form/input-form';
import { EffortLevel, ModelType } from '../../models';

@Component({
  selector: 'app-welcome-screen',
  standalone: true,
  imports: [InputFormComponent],
  templateUrl: './welcome-screen.html',
  styleUrl: './welcome-screen.scss'
})
export class WelcomeScreen {
  @Input() isLoading = false;

  @Output() submitForm = new EventEmitter<{
    inputValue: string;
    effort: EffortLevel;
    model: ModelType;
  }>();

  @Output() cancel = new EventEmitter<void>();

  onSubmit(data: { inputValue: string; effort: EffortLevel; model: ModelType }): void {
    this.submitForm.emit(data);
  }

  onCancel(): void {
    this.cancel.emit();
  }
}
