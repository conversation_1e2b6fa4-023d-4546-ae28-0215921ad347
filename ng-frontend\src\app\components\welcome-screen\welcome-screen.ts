import { Component, input, output } from '@angular/core';
import { InputForm } from '../input-form/input-form';
import { FormSubmission } from '../../models';

@Component({
  selector: 'app-welcome-screen',
  imports: [InputForm],
  templateUrl: './welcome-screen.html',
  styleUrl: './welcome-screen.scss'
})
export class WelcomeScreen {
  // Inputs using new signal-based input API
  isLoading = input<boolean>(false);

  // Outputs using new signal-based output API
  submitForm = output<FormSubmission>();
  cancel = output<void>();

  onSubmit(submission: FormSubmission): void {
    this.submitForm.emit(submission);
  }

  onCancel(): void {
    this.cancel.emit();
  }
}
