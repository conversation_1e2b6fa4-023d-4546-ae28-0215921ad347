export interface Message {
  type: 'human' | 'ai';
  content: string;
  id?: string;
}

export interface ProcessedEvent {
  title: string;
  data: any;
}

export interface StreamConfig {
  messages: Message[];
  initial_search_query_count: number;
  max_research_loops: number;
  reasoning_model: string;
}

export interface StreamResponse {
  generate_query?: {
    query_list: string[];
  };
  web_research?: {
    sources_gathered: Array<{
      label: string;
      [key: string]: any;
    }>;
  };
  reflection?: {
    is_sufficient: boolean;
    follow_up_queries: string[];
  };
  finalize_answer?: any;
}

export type EffortLevel = 'low' | 'medium' | 'high';
export type ModelType = 'gemini-2.0-flash' | 'gemini-2.5-flash-preview-04-17' | 'gemini-2.5-pro-preview-05-06';
