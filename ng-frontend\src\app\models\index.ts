// Core interfaces and types for the Angular frontend

export interface Message {
  id?: string;
  type: 'human' | 'ai';
  content: string | any;
}

export interface ProcessedEvent {
  title: string;
  data: any;
}

export interface StreamConfig {
  messages: Message[];
  initial_search_query_count: number;
  max_research_loops: number;
  reasoning_model: string;
}

export interface StreamEvent {
  generate_query?: {
    query_list: string[];
  };
  web_research?: {
    sources_gathered?: Array<{
      label?: string;
      [key: string]: any;
    }>;
  };
  reflection?: {
    is_sufficient: boolean;
    follow_up_queries: string[];
  };
  finalize_answer?: any;
}

export type EffortLevel = 'low' | 'medium' | 'high';
export type ModelType = 'gemini-2.0-flash' | 'gemini-2.5-flash-preview-04-17' | 'gemini-2.5-pro-preview-05-06';

export interface FormSubmission {
  inputValue: string;
  effort: EffortLevel;
  model: ModelType;
}
