import { Injectable, signal, computed } from '@angular/core';
import { BehaviorSubject, Subject } from 'rxjs';
import { Message, ProcessedEvent, StreamConfig, StreamResponse } from '../models';

@Injectable({
  providedIn: 'root'
})
export class StreamService {
  // Signals for reactive state management
  private _messages = signal<Message[]>([]);
  private _isLoading = signal<boolean>(false);
  private _processedEvents = signal<ProcessedEvent[]>([]);
  
  // Public readonly signals
  readonly messages = this._messages.asReadonly();
  readonly isLoading = this._isLoading.asReadonly();
  readonly processedEvents = this._processedEvents.asReadonly();
  
  // Subjects for event handling
  private finishSubject = new Subject<any>();
  private updateEventSubject = new Subject<StreamResponse>();
  
  // Computed signals
  readonly hasMessages = computed(() => this._messages().length > 0);
  
  private apiUrl = 'http://localhost:2024'; // Default development URL
  private assistantId = 'agent';
  private messagesKey = 'messages';
  
  constructor() {
    // Set up event stream processing
    this.updateEventSubject.subscribe(event => {
      this.processUpdateEvent(event);
    });
  }
  
  /**
   * Submit a new stream request
   */
  async submit(config: StreamConfig): Promise<void> {
    this._isLoading.set(true);
    this._messages.set(config.messages);
    
    try {
      const response = await fetch(`${this.apiUrl}/threads`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          assistant_id: this.assistantId,
          config: config,
        }),
      });
      
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
      
      const reader = response.body?.getReader();
      if (!reader) {
        throw new Error('No response body reader available');
      }
      
      await this.processStream(reader);
    } catch (error) {
      console.error('Stream error:', error);
      this._isLoading.set(false);
    }
  }
  
  /**
   * Stop the current stream
   */
  stop(): void {
    this._isLoading.set(false);
    // In a real implementation, you would cancel the fetch request
  }
  
  /**
   * Clear all messages and reset state
   */
  clear(): void {
    this._messages.set([]);
    this._processedEvents.set([]);
    this._isLoading.set(false);
  }
  
  /**
   * Process the streaming response
   */
  private async processStream(reader: ReadableStreamDefaultReader<Uint8Array>): Promise<void> {
    const decoder = new TextDecoder();
    
    try {
      while (true) {
        const { done, value } = await reader.read();
        
        if (done) {
          this._isLoading.set(false);
          break;
        }
        
        const chunk = decoder.decode(value, { stream: true });
        const lines = chunk.split('\n');
        
        for (const line of lines) {
          if (line.trim() && line.startsWith('data: ')) {
            try {
              const data = JSON.parse(line.slice(6));
              this.updateEventSubject.next(data);
            } catch (e) {
              console.warn('Failed to parse SSE data:', line);
            }
          }
        }
      }
    } catch (error) {
      console.error('Stream processing error:', error);
      this._isLoading.set(false);
    } finally {
      reader.releaseLock();
    }
  }
  
  /**
   * Process update events from the stream
   */
  private processUpdateEvent(event: StreamResponse): void {
    let processedEvent: ProcessedEvent | null = null;
    
    if (event.generate_query) {
      processedEvent = {
        title: "Generating Search Queries",
        data: event.generate_query.query_list.join(", "),
      };
    } else if (event.web_research) {
      const sources = event.web_research.sources_gathered || [];
      const numSources = sources.length;
      const uniqueLabels = [
        ...new Set(sources.map((s: any) => s.label).filter(Boolean)),
      ];
      const exampleLabels = uniqueLabels.slice(0, 3).join(", ");
      processedEvent = {
        title: "Web Research",
        data: `Gathered ${numSources} sources. Related to: ${
          exampleLabels || "N/A"
        }.`,
      };
    } else if (event.reflection) {
      processedEvent = {
        title: "Reflection",
        data: event.reflection.is_sufficient
          ? "Search successful, generating final answer."
          : `Need more information, searching for ${event.reflection.follow_up_queries.join(
              ", "
            )}`,
      };
    } else if (event.finalize_answer) {
      processedEvent = {
        title: "Finalizing Answer",
        data: "Composing and presenting the final answer.",
      };
    }
    
    if (processedEvent) {
      this._processedEvents.update(events => [...events, processedEvent!]);
    }
    
    // Handle message updates
    if (event.finalize_answer) {
      // Add AI response message
      const aiMessage: Message = {
        type: 'ai',
        content: event.finalize_answer.content || 'Response generated.',
        id: Date.now().toString(),
      };
      
      this._messages.update(messages => [...messages, aiMessage]);
      this._isLoading.set(false);
    }
  }
}
