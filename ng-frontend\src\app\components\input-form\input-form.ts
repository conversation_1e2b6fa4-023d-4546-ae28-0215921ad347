import { Component, input, output, signal } from '@angular/core';
import { <PERSON><PERSON><PERSON><PERSON>, FormGroup, ReactiveFormsModule, Validators } from '@angular/forms';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatSelectModule } from '@angular/material/select';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { CommonModule } from '@angular/common';
import { FormSubmission, EffortLevel, ModelType } from '../../models';

@Component({
  selector: 'app-input-form',
  imports: [
    CommonModule,
    ReactiveFormsModule,
    MatFormFieldModule,
    MatInputModule,
    MatSelectModule,
    MatButtonModule,
    MatIconModule
  ],
  templateUrl: './input-form.html',
  styleUrl: './input-form.scss'
})
export class InputForm {
  // Inputs using new signal-based input API
  isLoading = input<boolean>(false);
  hasHistory = input<boolean>(false);

  // Outputs using new signal-based output API
  submitForm = output<FormSubmission>();
  cancel = output<void>();

  // Form state
  form: FormGroup;

  // Effort options
  effortOptions: { value: EffortLevel; label: string }[] = [
    { value: 'low', label: 'Low' },
    { value: 'medium', label: 'Medium' },
    { value: 'high', label: 'High' }
  ];

  // Model options
  modelOptions: { value: ModelType; label: string; icon: string }[] = [
    { value: 'gemini-2.0-flash', label: '2.0 Flash', icon: 'flash_on' },
    { value: 'gemini-2.5-flash-preview-04-17', label: '2.5 Flash', icon: 'flash_on' },
    { value: 'gemini-2.5-pro-preview-05-06', label: '2.5 Pro', icon: 'memory' }
  ];

  constructor(private fb: FormBuilder) {
    this.form = this.fb.group({
      inputValue: ['', [Validators.required, Validators.minLength(1)]],
      effort: ['medium', Validators.required],
      model: ['gemini-2.5-flash-preview-04-17', Validators.required]
    });
  }

  onSubmit(): void {
    if (this.form.valid && !this.isLoading()) {
      const formValue = this.form.value;
      const submission: FormSubmission = {
        inputValue: formValue.inputValue.trim(),
        effort: formValue.effort,
        model: formValue.model
      };

      this.submitForm.emit(submission);
      this.form.patchValue({ inputValue: '' }); // Clear input after submit
    }
  }

  onCancel(): void {
    this.cancel.emit();
  }

  onKeyDown(event: KeyboardEvent): void {
    if (event.key === 'Enter' && !event.shiftKey) {
      event.preventDefault();
      this.onSubmit();
    }
  }

  newSearch(): void {
    window.location.reload();
  }

  get isSubmitDisabled(): boolean {
    return !this.form.get('inputValue')?.value?.trim() || this.isLoading();
  }
}
