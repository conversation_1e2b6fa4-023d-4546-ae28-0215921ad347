import { Component, Input, Output, EventEmitter, signal } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { ButtonComponent } from '../ui/button.component';
import { TextareaComponent } from '../ui/textarea.component';
import { SelectComponent, SelectOption } from '../ui/select.component';
import { EffortLevel, ModelType } from '../../models';

@Component({
  selector: 'app-input-form',
  standalone: true,
  imports: [CommonModule, FormsModule, ButtonComponent, TextareaComponent, SelectComponent],
  templateUrl: './input-form.html',
  styleUrl: './input-form.scss'
})
export class InputFormComponent {
  @Input() isLoading = false;
  @Input() hasHistory = false;

  @Output() submitForm = new EventEmitter<{
    inputValue: string;
    effort: EffortLevel;
    model: ModelType;
  }>();

  @Output() cancel = new EventEmitter<void>();

  // Form state using signals
  inputValue = signal('');
  effort = signal<EffortLevel>('medium');
  model = signal<ModelType>('gemini-2.5-flash-preview-04-17');

  // Select options
  effortOptions: SelectOption[] = [
    { value: 'low', label: 'Low' },
    { value: 'medium', label: 'Medium' },
    { value: 'high', label: 'High' }
  ];

  modelOptions: SelectOption[] = [
    { value: 'gemini-2.0-flash', label: '2.0 Flash' },
    { value: 'gemini-2.5-flash-preview-04-17', label: '2.5 Flash' },
    { value: 'gemini-2.5-pro-preview-05-06', label: '2.5 Pro' }
  ];

  get isSubmitDisabled(): boolean {
    return !this.inputValue().trim() || this.isLoading;
  }

  onSubmit(event?: Event): void {
    if (event) {
      event.preventDefault();
    }

    if (!this.inputValue().trim()) return;

    this.submitForm.emit({
      inputValue: this.inputValue(),
      effort: this.effort(),
      model: this.model()
    });

    this.inputValue.set('');
  }

  onKeyDown(event: KeyboardEvent): void {
    if (event.key === 'Enter' && !event.shiftKey) {
      event.preventDefault();
      this.onSubmit();
    }
  }

  onCancel(): void {
    this.cancel.emit();
  }

  onNewSearch(): void {
    window.location.reload();
  }
}
