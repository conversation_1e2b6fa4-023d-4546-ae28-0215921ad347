<app-card class="border-none rounded-lg bg-neutral-700 max-h-96">
  <app-card-header>
    <app-card-description class="flex items-center justify-between">
      <div
        class="flex items-center justify-start text-sm w-full cursor-pointer gap-2 text-neutral-100"
        (click)="toggleTimeline()">
        Research
        @if (isTimelineCollapsed()) {
          <!-- ChevronDown Icon -->
          <svg class="h-4 w-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <polyline points="6,9 12,15 18,9"></polyline>
          </svg>
        } @else {
          <!-- ChevronUp Icon -->
          <svg class="h-4 w-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <polyline points="18,15 12,9 6,15"></polyline>
          </svg>
        }
      </div>
    </app-card-description>
  </app-card-header>

  @if (!isTimelineCollapsed()) {
    <app-scroll-area class="max-h-96 overflow-y-auto">
      <app-card-content>
        @if (isLoading && processedEvents.length === 0) {
          <div class="relative pl-8 pb-4">
            <div class="absolute left-3 top-3.5 h-full w-0.5 bg-neutral-800"></div>
            <div class="absolute left-0.5 top-2 h-5 w-5 rounded-full bg-neutral-800 flex items-center justify-center ring-4 ring-neutral-900">
              <!-- Loader Icon -->
              <svg class="h-3 w-3 text-neutral-400 animate-spin" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
              </svg>
            </div>
            <div>
              <p class="text-sm text-neutral-300 font-medium">
                Searching...
              </p>
            </div>
          </div>
        }

        @if (processedEvents.length > 0) {
          <div class="space-y-0">
            @for (eventItem of processedEvents; track $index) {
              <div class="relative pl-8 pb-4">
                @if ($index < processedEvents.length - 1 || (isLoading && $index === processedEvents.length - 1)) {
                  <div class="absolute left-3 top-3.5 h-full w-0.5 bg-neutral-600"></div>
                }

                <div class="absolute left-0.5 top-2 h-6 w-6 rounded-full bg-neutral-600 flex items-center justify-center ring-4 ring-neutral-700">
                  @switch (getEventIcon(eventItem.title, $index)) {
                    @case ('loader') {
                      <svg class="h-4 w-4 text-neutral-400 animate-spin" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                      </svg>
                    }
                    @case ('text-search') {
                      <svg class="h-4 w-4 text-neutral-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <circle cx="11" cy="11" r="8"></circle>
                        <path d="m21 21-4.35-4.35"></path>
                        <path d="M11 6a5 5 0 0 1 5 5"></path>
                      </svg>
                    }
                    @case ('brain') {
                      <svg class="h-4 w-4 text-neutral-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z"></path>
                      </svg>
                    }
                    @case ('search') {
                      <svg class="h-4 w-4 text-neutral-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <circle cx="11" cy="11" r="8"></circle>
                        <path d="m21 21-4.35-4.35"></path>
                      </svg>
                    }
                    @case ('pen') {
                      <svg class="h-4 w-4 text-neutral-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15.232 5.232l3.536 3.536m-2.036-5.036a2.5 2.5 0 113.536 3.536L6.5 21.036H3v-3.572L16.732 3.732z"></path>
                      </svg>
                    }
                    @default {
                      <svg class="h-4 w-4 text-neutral-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <polyline points="22,12 18,12 15,21 9,3 6,12 2,12"></polyline>
                      </svg>
                    }
                  }
                </div>

                <div>
                  <p class="text-sm text-neutral-200 font-medium mb-0.5">
                    {{ eventItem.title }}
                  </p>
                  <p class="text-xs text-neutral-300 leading-relaxed">
                    {{ formatEventData(eventItem.data) }}
                  </p>
                </div>
              </div>
            }

            @if (isLoading && processedEvents.length > 0) {
              <div class="relative pl-8 pb-4">
                <div class="absolute left-0.5 top-2 h-5 w-5 rounded-full bg-neutral-600 flex items-center justify-center ring-4 ring-neutral-700">
                  <svg class="h-3 w-3 text-neutral-400 animate-spin" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                  </svg>
                </div>
                <div>
                  <p class="text-sm text-neutral-300 font-medium">
                    Searching...
                  </p>
                </div>
              </div>
            }
          </div>
        } @else if (!isLoading) {
          <div class="flex flex-col items-center justify-center h-full text-neutral-500 pt-10">
            <!-- Info Icon -->
            <svg class="h-6 w-6 mb-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <circle cx="12" cy="12" r="10"></circle>
              <line x1="12" y1="16" x2="12" y2="12"></line>
              <line x1="12" y1="8" x2="12.01" y2="8"></line>
            </svg>
            <p class="text-sm">No activity to display.</p>
            <p class="text-xs text-neutral-600 mt-1">
              Timeline will update during processing.
            </p>
          </div>
        }
      </app-card-content>
    </app-scroll-area>
  }
</app-card>
