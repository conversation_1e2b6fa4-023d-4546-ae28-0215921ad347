<mat-card class="timeline-card">
  <mat-card-header>
    <div class="timeline-header" (click)="toggleCollapse()">
      <span class="timeline-title">Research</span>
      <mat-icon class="collapse-icon">
        {{ isCollapsed() ? 'expand_more' : 'expand_less' }}
      </mat-icon>
    </div>
  </mat-card-header>

  <mat-card-content *ngIf="!isCollapsed()" class="timeline-content">
    <!-- Loading state with no events -->
    <div *ngIf="isLoading() && processedEvents().length === 0" class="timeline-item">
      <div class="timeline-line"></div>
      <div class="timeline-icon-container">
        <mat-spinner diameter="20" class="timeline-spinner"></mat-spinner>
      </div>
      <div class="timeline-content-item">
        <p class="timeline-item-title">Searching...</p>
      </div>
    </div>

    <!-- Events list -->
    <div *ngIf="processedEvents().length > 0" class="timeline-events">
      <div
        *ngFor="let event of processedEvents(); let i = index; trackBy: trackByIndex"
        class="timeline-item">

        <!-- Timeline line (show if not last item or if loading) -->
        <div
          *ngIf="i < processedEvents().length - 1 || (isLoading() && i === processedEvents().length - 1)"
          class="timeline-line">
        </div>

        <!-- Timeline icon -->
        <div class="timeline-icon-container">
          <mat-spinner
            *ngIf="shouldShowSpinner(event.title, i); else staticIcon"
            diameter="16"
            class="timeline-spinner-small">
          </mat-spinner>

          <ng-template #staticIcon>
            <mat-icon class="timeline-icon">{{ getEventIcon(event.title, i) }}</mat-icon>
          </ng-template>
        </div>

        <!-- Event content -->
        <div class="timeline-content-item">
          <p class="timeline-item-title">{{ event.title }}</p>
          <p class="timeline-item-data">{{ formatEventData(event.data) }}</p>
        </div>
      </div>

      <!-- Loading indicator at end of events -->
      <div *ngIf="isLoading() && processedEvents().length > 0" class="timeline-item">
        <div class="timeline-icon-container">
          <mat-spinner diameter="16" class="timeline-spinner-small"></mat-spinner>
        </div>
        <div class="timeline-content-item">
          <p class="timeline-item-title">Searching...</p>
        </div>
      </div>
    </div>

    <!-- No activity state -->
    <div *ngIf="!isLoading() && processedEvents().length === 0" class="no-activity">
      <mat-icon class="no-activity-icon">info</mat-icon>
      <p class="no-activity-title">No activity to display.</p>
      <p class="no-activity-subtitle">Timeline will update during processing.</p>
    </div>
  </mat-card-content>
</mat-card>
