<form (ngSubmit)="onSubmit($event)" class="flex flex-col gap-2 p-3">
  <div class="flex flex-row items-center justify-between text-white rounded-3xl rounded-bl-sm break-words min-h-7 bg-neutral-700 px-4 pt-3"
       [class.rounded-br-sm]="hasHistory">
    <app-textarea
      [(ngModel)]="inputValue"
      name="inputValue"
      placeholder="Who won the Euro 2024 and scored the most goals?"
      class="w-full text-neutral-100 placeholder-neutral-500 resize-none border-0 focus:outline-none focus:ring-0 outline-none focus-visible:ring-0 shadow-none md:text-base min-h-[56px] max-h-[200px]"
      [rows]="1"
      (onKeyDown)="onKeyDown($event)">
    </app-textarea>

    <div class="-mt-3">
      @if (isLoading) {
        <app-button
          type="button"
          variant="ghost"
          size="icon"
          class="text-red-500 hover:text-red-400 hover:bg-red-500/10 p-2 cursor-pointer rounded-full transition-all duration-200"
          (onClick)="onCancel()">
          <!-- Stop Circle Icon -->
          <svg class="h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <circle cx="12" cy="12" r="10"></circle>
            <rect x="9" y="9" width="6" height="6" rx="1"></rect>
          </svg>
        </app-button>
      } @else {
        <app-button
          type="submit"
          variant="ghost"
          [disabled]="isSubmitDisabled"
          [className]="isSubmitDisabled
            ? 'text-neutral-500'
            : 'text-blue-500 hover:text-blue-400 hover:bg-blue-500/10'"
          class="p-2 cursor-pointer rounded-full transition-all duration-200 text-base">
          Search
          <!-- Send Icon -->
          <svg class="h-5 w-5 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 19l9 2-9-18-9 18 9-2zm0 0v-8"></path>
          </svg>
        </app-button>
      }
    </div>
  </div>

  <div class="flex items-center justify-between">
    <div class="flex flex-row gap-2">
      <!-- Effort Selection -->
      <div class="flex flex-row gap-2 bg-neutral-700 border-neutral-600 text-neutral-300 focus:ring-neutral-500 rounded-xl rounded-t-sm pl-2 max-w-[100%] sm:max-w-[90%]">
        <div class="flex flex-row items-center text-sm">
          <!-- Brain Icon -->
          <svg class="h-4 w-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z"></path>
          </svg>
          Effort
        </div>
        <app-select
          [(ngModel)]="effort"
          name="effort"
          [options]="effortOptions"
          placeholder="Effort"
          class="w-[120px] bg-transparent border-none cursor-pointer">
        </app-select>
      </div>

      <!-- Model Selection -->
      <div class="flex flex-row gap-2 bg-neutral-700 border-neutral-600 text-neutral-300 focus:ring-neutral-500 rounded-xl rounded-t-sm pl-2 max-w-[100%] sm:max-w-[90%]">
        <div class="flex flex-row items-center text-sm ml-2">
          <!-- CPU Icon -->
          <svg class="h-4 w-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <rect x="4" y="4" width="16" height="16" rx="2" ry="2"></rect>
            <rect x="9" y="9" width="6" height="6"></rect>
            <line x1="9" y1="1" x2="9" y2="4"></line>
            <line x1="15" y1="1" x2="15" y2="4"></line>
            <line x1="9" y1="20" x2="9" y2="23"></line>
            <line x1="15" y1="20" x2="15" y2="23"></line>
            <line x1="20" y1="9" x2="23" y2="9"></line>
            <line x1="20" y1="14" x2="23" y2="14"></line>
            <line x1="1" y1="9" x2="4" y2="9"></line>
            <line x1="1" y1="14" x2="4" y2="14"></line>
          </svg>
          Model
        </div>
        <app-select
          [(ngModel)]="model"
          name="model"
          [options]="modelOptions"
          placeholder="Model"
          class="w-[150px] bg-transparent border-none cursor-pointer">
        </app-select>
      </div>
    </div>

    @if (hasHistory) {
      <app-button
        class="bg-neutral-700 border-neutral-600 text-neutral-300 cursor-pointer rounded-xl rounded-t-sm pl-2"
        variant="default"
        (onClick)="onNewSearch()">
        <!-- Square Pen Icon -->
        <svg class="h-4 w-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15.232 5.232l3.536 3.536m-2.036-5.036a2.5 2.5 0 113.536 3.536L6.5 21.036H3v-3.572L16.732 3.732z"></path>
        </svg>
        New Search
      </app-button>
    }
  </div>
</form>
