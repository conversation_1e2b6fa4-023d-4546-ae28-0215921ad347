<form [formGroup]="form" (ngSubmit)="onSubmit()" class="input-form">
  <!-- Main input area -->
  <div class="input-container" [class.has-history]="hasHistory()">
    <div class="input-wrapper">
      <mat-form-field appearance="fill" class="input-field">
        <textarea
          matInput
          formControlName="inputValue"
          placeholder="Who won the Euro 2024 and scored the most goals?"
          (keydown)="onKeyDown($event)"
          rows="1"
          class="input-textarea">
        </textarea>
      </mat-form-field>

      <div class="action-button">
        <button
          *ngIf="isLoading(); else submitButton"
          mat-icon-button
          type="button"
          class="cancel-button"
          (click)="onCancel()">
          <mat-icon>stop_circle</mat-icon>
        </button>

        <ng-template #submitButton>
          <button
            mat-raised-button
            type="submit"
            class="submit-button"
            [disabled]="isSubmitDisabled">
            Search
            <mat-icon>send</mat-icon>
          </button>
        </ng-template>
      </div>
    </div>
  </div>

  <!-- Controls row -->
  <div class="controls-row">
    <div class="controls-left">
      <!-- Effort selector -->
      <div class="control-group">
        <mat-icon class="control-icon">psychology</mat-icon>
        <span class="control-label">Effort</span>
        <mat-form-field appearance="fill" class="control-select">
          <mat-select formControlName="effort">
            <mat-option *ngFor="let option of effortOptions" [value]="option.value">
              {{ option.label }}
            </mat-option>
          </mat-select>
        </mat-form-field>
      </div>

      <!-- Model selector -->
      <div class="control-group">
        <mat-icon class="control-icon">memory</mat-icon>
        <span class="control-label">Model</span>
        <mat-form-field appearance="fill" class="control-select">
          <mat-select formControlName="model">
            <mat-option *ngFor="let option of modelOptions" [value]="option.value">
              <div class="model-option">
                <mat-icon [class]="'model-icon-' + option.value">{{ option.icon }}</mat-icon>
                {{ option.label }}
              </div>
            </mat-option>
          </mat-select>
        </mat-form-field>
      </div>
    </div>

    <!-- New search button (only shown when hasHistory is true) -->
    <div class="controls-right" *ngIf="hasHistory()">
      <button
        mat-raised-button
        type="button"
        class="new-search-button"
        (click)="newSearch()">
        <mat-icon>edit_square</mat-icon>
        New Search
      </button>
    </div>
  </div>
</form>
