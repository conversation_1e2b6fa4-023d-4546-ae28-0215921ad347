import { Component, Input } from '@angular/core';
import { CommonModule } from '@angular/common';
import { cn } from '../../lib/utils';

@Component({
  selector: 'app-card',
  standalone: true,
  imports: [CommonModule],
  template: `
    <div [class]="computedClasses">
      <ng-content></ng-content>
    </div>
  `,
})
export class CardComponent {
  @Input() className = '';
  
  get computedClasses(): string {
    return cn(
      "rounded-lg border bg-card text-card-foreground shadow-sm",
      this.className
    );
  }
}

@Component({
  selector: 'app-card-header',
  standalone: true,
  imports: [CommonModule],
  template: `
    <div [class]="computedClasses">
      <ng-content></ng-content>
    </div>
  `,
})
export class CardHeaderComponent {
  @Input() className = '';
  
  get computedClasses(): string {
    return cn("flex flex-col space-y-1.5 p-6", this.className);
  }
}

@Component({
  selector: 'app-card-title',
  standalone: true,
  imports: [CommonModule],
  template: `
    <h3 [class]="computedClasses">
      <ng-content></ng-content>
    </h3>
  `,
})
export class CardTitleComponent {
  @Input() className = '';
  
  get computedClasses(): string {
    return cn(
      "text-2xl font-semibold leading-none tracking-tight",
      this.className
    );
  }
}

@Component({
  selector: 'app-card-description',
  standalone: true,
  imports: [CommonModule],
  template: `
    <p [class]="computedClasses">
      <ng-content></ng-content>
    </p>
  `,
})
export class CardDescriptionComponent {
  @Input() className = '';
  
  get computedClasses(): string {
    return cn("text-sm text-muted-foreground", this.className);
  }
}

@Component({
  selector: 'app-card-content',
  standalone: true,
  imports: [CommonModule],
  template: `
    <div [class]="computedClasses">
      <ng-content></ng-content>
    </div>
  `,
})
export class CardContentComponent {
  @Input() className = '';
  
  get computedClasses(): string {
    return cn("p-6 pt-0", this.className);
  }
}

@Component({
  selector: 'app-card-footer',
  standalone: true,
  imports: [CommonModule],
  template: `
    <div [class]="computedClasses">
      <ng-content></ng-content>
    </div>
  `,
})
export class CardFooterComponent {
  @Input() className = '';
  
  get computedClasses(): string {
    return cn("flex items-center p-6 pt-0", this.className);
  }
}
