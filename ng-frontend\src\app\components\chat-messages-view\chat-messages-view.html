<div class="flex flex-col h-full">
  <app-scroll-area class="flex-grow" #scrollArea>
    <div class="p-4 md:p-6 space-y-2 max-w-4xl mx-auto pt-16">
      @for (message of messages; track message.id || $index) {
        <div class="space-y-3">
          <div class="flex items-start gap-3"
               [class.justify-end]="message.type === 'human'">

            @if (message.type === 'human') {
              <!-- Human Message Bubble -->
              <div class="text-white rounded-3xl break-words min-h-7 bg-neutral-700 max-w-[100%] sm:max-w-[90%] px-4 pt-3 rounded-br-lg">
                <div [innerHTML]="renderMarkdown(message.content)"></div>
              </div>
            } @else {
              <!-- AI Message Bubble -->
              <div class="relative break-words flex flex-col">
                @if (getHistoricalActivity(message.id!) || (shouldShowLiveActivity($index) && liveActivityEvents.length > 0)) {
                  <div class="mb-3 border-b border-neutral-700 pb-3 text-xs">
                    <app-activity-timeline
                      [processedEvents]="shouldShowLiveActivity($index) ? liveActivityEvents : getHistoricalActivity(message.id!)!"
                      [isLoading]="shouldShowLiveActivity($index)">
                    </app-activity-timeline>
                  </div>
                }

                <div [innerHTML]="renderMarkdown(message.content)"></div>

                <app-button
                  variant="default"
                  class="cursor-pointer bg-neutral-700 border-neutral-600 text-neutral-300 self-end mt-2"
                  (onClick)="handleCopy(message.content, message.id!)">
                  @if (copiedMessageId() === message.id) {
                    Copied
                    <!-- CopyCheck Icon -->
                    <svg class="h-4 w-4 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                    </svg>
                  } @else {
                    Copy
                    <!-- Copy Icon -->
                    <svg class="h-4 w-4 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z"></path>
                    </svg>
                  }
                </app-button>
              </div>
            }
          </div>
        </div>
      }

      @if (shouldShowLoadingMessage()) {
        <div class="flex items-start gap-3 mt-3">
          <div class="relative group max-w-[85%] md:max-w-[80%] rounded-xl p-3 shadow-sm break-words bg-neutral-800 text-neutral-100 rounded-bl-none w-full min-h-[56px]">
            @if (liveActivityEvents.length > 0) {
              <div class="text-xs">
                <app-activity-timeline
                  [processedEvents]="liveActivityEvents"
                  [isLoading]="true">
                </app-activity-timeline>
              </div>
            } @else {
              <div class="flex items-center justify-start h-full">
                <!-- Loader Icon -->
                <svg class="h-5 w-5 animate-spin text-neutral-400 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                </svg>
                <span>Processing...</span>
              </div>
            }
          </div>
        </div>
      }
    </div>
  </app-scroll-area>

  <app-input-form
    [isLoading]="isLoading"
    [hasHistory]="messages.length > 0"
    (submitForm)="onSubmit($event)"
    (cancel)="onCancel()">
  </app-input-form>
</div>
