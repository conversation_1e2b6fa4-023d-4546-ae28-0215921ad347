<div class="chat-container">
  <!-- Messages area -->
  <div class="messages-area" #scrollContainer>
    <div class="messages-content">
      <!-- Message list -->
      <div
        *ngFor="let message of messages(); let i = index; trackBy: trackByMessageId"
        class="message-wrapper">

        <div class="message-container" [class.human-message]="message.type === 'human'">
          <!-- Human message -->
          <div *ngIf="message.type === 'human'" class="human-message-bubble">
            <div [innerHTML]="renderMarkdown(getMessageContent(message))"></div>
          </div>

          <!-- AI message -->
          <div *ngIf="message.type === 'ai'" class="ai-message-bubble">
            <!-- Activity timeline for this message -->
            <div
              *ngIf="getHistoricalActivity(message.id!) || (i === messages().length - 1 && isLoading() && liveActivityEvents().length > 0)"
              class="message-activity">
              <app-activity-timeline
                [processedEvents]="i === messages().length - 1 && isLoading() ? liveActivityEvents() : getHistoricalActivity(message.id!) || []"
                [isLoading]="i === messages().length - 1 && isLoading()">
              </app-activity-timeline>
            </div>

            <!-- Message content -->
            <div class="ai-message-content">
              <div [innerHTML]="renderMarkdown(getMessageContent(message))"></div>

              <!-- Copy button -->
              <button
                mat-stroked-button
                class="copy-button"
                (click)="copyMessage(message)">
                <mat-icon>{{ copiedMessageId() === message.id ? 'check' : 'content_copy' }}</mat-icon>
                {{ copiedMessageId() === message.id ? 'Copied' : 'Copy' }}
              </button>
            </div>
          </div>
        </div>
      </div>

      <!-- Loading indicator when no messages or last message is human -->
      <div
        *ngIf="isLoading() && (messages().length === 0 || messages()[messages().length - 1].type === 'human')"
        class="loading-message">

        <div class="ai-message-bubble">
          <!-- Live activity timeline -->
          <div *ngIf="liveActivityEvents().length > 0; else simpleLoader" class="message-activity">
            <app-activity-timeline
              [processedEvents]="liveActivityEvents()"
              [isLoading]="true">
            </app-activity-timeline>
          </div>

          <!-- Simple loader fallback -->
          <ng-template #simpleLoader>
            <div class="simple-loader">
              <mat-spinner diameter="20"></mat-spinner>
              <span>Processing...</span>
            </div>
          </ng-template>
        </div>
      </div>
    </div>
  </div>

  <!-- Input form -->
  <div class="input-area">
    <app-input-form
      [isLoading]="isLoading()"
      [hasHistory]="messages().length > 0"
      (submitForm)="onSubmit($event)"
      (cancel)="onCancel()">
    </app-input-form>
  </div>
</div>
