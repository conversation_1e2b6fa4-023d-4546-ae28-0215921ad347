.input-form {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  padding: 0.75rem;
}

.input-container {
  &.has-history {
    .input-wrapper {
      border-radius: 1.5rem 1.5rem 0.25rem 0.25rem;
    }
  }
}

.input-wrapper {
  display: flex;
  align-items: center;
  justify-content: space-between;
  background-color: #404040;
  border-radius: 1.5rem 1.5rem 0.25rem 1.5rem;
  padding: 0.75rem 1rem;
  min-height: 3.5rem;
}

.input-field {
  flex: 1;
  margin-right: 0.75rem;

  ::ng-deep {
    .mat-mdc-form-field-subscript-wrapper {
      display: none;
    }

    .mat-mdc-form-field-infix {
      border: none;
      padding: 0;
    }

    .mat-mdc-text-field-wrapper {
      background-color: transparent;
      border-radius: 0;
    }

    .mdc-text-field--filled {
      background-color: transparent;
    }

    .mat-mdc-form-field-focus-overlay {
      background-color: transparent;
    }
  }
}

.input-textarea {
  color: #f5f5f5;
  background: transparent;
  border: none;
  outline: none;
  resize: none;
  min-height: 3.5rem;
  max-height: 12.5rem;

  &::placeholder {
    color: #737373;
  }
}

.action-button {
  margin-top: -0.75rem;
}

.cancel-button {
  color: #ef4444;

  &:hover {
    color: #dc2626;
    background-color: rgba(239, 68, 68, 0.1);
  }
}

.submit-button {
  background-color: #3b82f6;
  color: white;

  &:hover:not(:disabled) {
    background-color: #2563eb;
  }

  &:disabled {
    background-color: #737373;
    color: #a3a3a3;
  }
}

.controls-row {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.controls-left {
  display: flex;
  gap: 0.5rem;
  flex-wrap: wrap;
}

.control-group {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  background-color: #404040;
  border-radius: 0.75rem 0.75rem 0.25rem 0.75rem;
  padding: 0.5rem;
  max-width: 100%;

  @media (min-width: 640px) {
    max-width: 90%;
  }
}

.control-icon {
  color: #f5f5f5;
  font-size: 1rem;
  width: 1rem;
  height: 1rem;
}

.control-label {
  font-size: 0.875rem;
  color: #f5f5f5;
  white-space: nowrap;
}

.control-select {
  min-width: 7.5rem;

  ::ng-deep {
    .mat-mdc-form-field-subscript-wrapper {
      display: none;
    }

    .mat-mdc-text-field-wrapper {
      background-color: transparent;
    }

    .mdc-text-field--filled {
      background-color: transparent;
    }

    .mat-mdc-select-value {
      color: #f5f5f5;
    }

    .mat-mdc-select-arrow {
      color: #f5f5f5;
    }
  }
}

.model-option {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.model-icon-gemini-2\.0-flash {
  color: #fbbf24;
}

.model-icon-gemini-2\.5-flash-preview-04-17 {
  color: #fb923c;
}

.model-icon-gemini-2\.5-pro-preview-05-06 {
  color: #a855f7;
}

.new-search-button {
  background-color: #404040;
  color: #f5f5f5;
  border-radius: 0.75rem 0.75rem 0.25rem 0.75rem;

  &:hover {
    background-color: #525252;
  }
}
