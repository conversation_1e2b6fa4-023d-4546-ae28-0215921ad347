import { Component, computed, signal, effect } from '@angular/core';
import { RouterOutlet } from '@angular/router';
import { WelcomeScreen } from './components/welcome-screen/welcome-screen';
import { ChatMessagesViewComponent } from './components/chat-messages-view/chat-messages-view';
import { StreamService } from './services/stream.service';
import { Message, ProcessedEvent, EffortLevel, ModelType } from './models';

@Component({
  selector: 'app-root',
  standalone: true,
  imports: [RouterOutlet, WelcomeScreen, ChatMessagesViewComponent],
  templateUrl: './app.html',
  styleUrl: './app.scss'
})
export class App {
  protected title = 'ng-frontend';

  // State from StreamService
  messages = this.streamService.messages;
  isLoading = this.streamService.isLoading;
  processedEvents = this.streamService.processedEvents;
  hasMessages = this.streamService.hasMessages;

  // Local state
  historicalActivities = signal<Record<string, ProcessedEvent[]>>({});
  hasFinalizeEventOccurred = signal<boolean>(false);

  constructor(private streamService: StreamService) {
    // Effect to handle finalization and store historical activities
    effect(() => {
      const events = this.processedEvents();
      const messages = this.messages();
      const isLoading = this.isLoading();

      // Check if finalize event occurred
      const hasFinalize = events.some(event =>
        event.title.toLowerCase().includes('finalizing')
      );

      if (hasFinalize) {
        this.hasFinalizeEventOccurred.set(true);
      }

      // Store historical activities when loading completes
      if (this.hasFinalizeEventOccurred() && !isLoading && messages.length > 0) {
        const lastMessage = messages[messages.length - 1];
        if (lastMessage && lastMessage.type === 'ai' && lastMessage.id) {
          this.historicalActivities.update(prev => ({
            ...prev,
            [lastMessage.id!]: [...events]
          }));
        }
        this.hasFinalizeEventOccurred.set(false);
      }
    });
  }

  async handleSubmit(data: { inputValue: string; effort: EffortLevel; model: ModelType }): Promise<void> {
    if (!data.inputValue.trim()) return;

    // Reset state
    this.hasFinalizeEventOccurred.set(false);

    // Convert effort to search parameters
    let initial_search_query_count = 0;
    let max_research_loops = 0;

    switch (data.effort) {
      case 'low':
        initial_search_query_count = 1;
        max_research_loops = 1;
        break;
      case 'medium':
        initial_search_query_count = 3;
        max_research_loops = 3;
        break;
      case 'high':
        initial_search_query_count = 5;
        max_research_loops = 10;
        break;
    }

    // Create new message
    const newMessage: Message = {
      type: 'human',
      content: data.inputValue,
      id: Date.now().toString(),
    };

    const newMessages = [...this.messages(), newMessage];

    // Submit to stream service
    await this.streamService.submit({
      messages: newMessages,
      initial_search_query_count,
      max_research_loops,
      reasoning_model: data.model,
    });
  }

  handleCancel(): void {
    this.streamService.stop();
    window.location.reload();
  }
}
