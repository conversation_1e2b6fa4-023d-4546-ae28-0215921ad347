import { Component, computed, inject } from '@angular/core';
import { CommonModule } from '@angular/common';
import { WelcomeScreen } from './components/welcome-screen/welcome-screen';
import { ChatMessagesView } from './components/chat-messages-view/chat-messages-view';
import { LangGraphService } from './services/langgraph.service';
import { FormSubmission, Message, EffortLevel, ModelType } from './models';

@Component({
  selector: 'app-root',
  imports: [
    CommonModule,
    WelcomeScreen,
    ChatMessagesView
  ],
  templateUrl: './app.html',
  styleUrl: './app.scss'
})
export class App {
  private langGraphService = inject(LangGraphService);

  // Computed signals for reactive UI
  messages = this.langGraphService.messages;
  isLoading = this.langGraphService.isLoading;
  processedEvents = this.langGraphService.processedEvents;
  historicalActivities = this.langGraphService.historicalActivities;

  // Computed to determine if we should show welcome screen
  showWelcomeScreen = computed(() => this.messages().length === 0);

  async onSubmit(submission: FormSubmission): Promise<void> {
    if (!submission.inputValue.trim()) return;

    // Convert effort to search parameters (matching React logic)
    let initial_search_query_count = 0;
    let max_research_loops = 0;

    switch (submission.effort) {
      case 'low':
        initial_search_query_count = 1;
        max_research_loops = 1;
        break;
      case 'medium':
        initial_search_query_count = 3;
        max_research_loops = 3;
        break;
      case 'high':
        initial_search_query_count = 5;
        max_research_loops = 10;
        break;
    }

    // Create new message
    const newMessage: Message = {
      type: 'human',
      content: submission.inputValue,
      id: Date.now().toString()
    };

    // Add to existing messages
    const newMessages: Message[] = [
      ...this.messages(),
      newMessage
    ];

    // Submit to LangGraph service
    await this.langGraphService.submitMessage({
      messages: newMessages,
      initial_search_query_count,
      max_research_loops,
      reasoning_model: submission.model
    });
  }

  onCancel(): void {
    this.langGraphService.stop();
    window.location.reload();
  }
}
