{"name": "ng-frontend", "version": "0.0.0", "scripts": {"ng": "ng", "start": "ng serve", "build": "ng build", "watch": "ng build --watch --configuration development", "test": "ng test"}, "private": true, "dependencies": {"@angular/cdk": "20.0.2", "@angular/common": "^20.0.0", "@angular/compiler": "^20.0.0", "@angular/core": "^20.0.0", "@angular/forms": "^20.0.0", "@angular/material": "20.0.2", "@angular/platform-browser": "^20.0.0", "@angular/router": "^20.0.0", "@langchain/core": "^0.3.57", "@langchain/langgraph-sdk": "^0.0.83", "@tailwindcss/typography": "^0.5.16", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "lucide-angular": "^0.513.0", "marked": "^15.0.12", "rxjs": "~7.8.0", "tailwind-merge": "^3.3.0", "tailwindcss": "^4.1.8", "tslib": "^2.3.0"}, "devDependencies": {"@angular/build": "^20.0.1", "@angular/cli": "^20.0.1", "@angular/compiler-cli": "^20.0.0", "@types/jasmine": "~5.1.0", "jasmine-core": "~5.7.0", "karma": "~6.4.0", "karma-chrome-launcher": "~3.2.0", "karma-coverage": "~2.2.0", "karma-jasmine": "~5.1.0", "karma-jasmine-html-reporter": "~2.1.0", "typescript": "~5.8.2"}, "packageManager": "pnpm@10.11.0"}