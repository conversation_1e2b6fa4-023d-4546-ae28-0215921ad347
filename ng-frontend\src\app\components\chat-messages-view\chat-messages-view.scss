.chat-container {
  display: flex;
  flex-direction: column;
  height: 100%;
}

.messages-area {
  flex: 1;
  overflow-y: auto;
  padding: 1rem 1.5rem 0 1.5rem;

  @media (min-width: 768px) {
    padding: 1.5rem;
  }
}

.messages-content {
  max-width: 64rem;
  margin: 0 auto;
  padding-top: 4rem;
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.message-wrapper {
  margin-bottom: 0.75rem;
}

.message-container {
  display: flex;
  align-items: flex-start;
  gap: 0.75rem;

  &.human-message {
    justify-content: flex-end;
  }
}

.human-message-bubble {
  background-color: #404040;
  color: white;
  border-radius: 1.5rem 1.5rem 0.5rem 1.5rem;
  padding: 0.75rem 1rem;
  max-width: 100%;
  word-break: break-word;
  min-height: 1.75rem;

  @media (min-width: 640px) {
    max-width: 90%;
  }

  ::ng-deep {
    p {
      margin: 0;
      line-height: 1.5;
    }

    h1, h2, h3, h4, h5, h6 {
      margin: 1rem 0 0.5rem 0;
      font-weight: 600;

      &:first-child {
        margin-top: 0;
      }
    }

    h1 { font-size: 1.5rem; }
    h2 { font-size: 1.25rem; }
    h3 { font-size: 1.125rem; }

    ul, ol {
      margin: 0.75rem 0;
      padding-left: 1.5rem;
    }

    li {
      margin-bottom: 0.25rem;
    }

    blockquote {
      border-left: 0.25rem solid #525252;
      padding-left: 1rem;
      margin: 0.75rem 0;
      font-style: italic;
      color: #d4d4d4;
    }

    code {
      background-color: #171717;
      border-radius: 0.25rem;
      padding: 0.125rem 0.25rem;
      font-family: 'Courier New', monospace;
      font-size: 0.875rem;
    }

    pre {
      background-color: #171717;
      border-radius: 0.5rem;
      padding: 0.75rem;
      overflow-x: auto;
      margin: 0.75rem 0;

      code {
        background: none;
        padding: 0;
      }
    }

    a {
      color: #60a5fa;
      text-decoration: none;

      &:hover {
        color: #93c5fd;
        text-decoration: underline;
      }
    }

    table {
      border-collapse: collapse;
      width: 100%;
      margin: 0.75rem 0;
    }

    th, td {
      border: 1px solid #525252;
      padding: 0.5rem 0.75rem;
      text-align: left;
    }

    th {
      background-color: #262626;
      font-weight: 600;
    }
  }
}

.ai-message-bubble {
  position: relative;
  display: flex;
  flex-direction: column;
  background-color: #262626;
  color: #f5f5f5;
  border-radius: 0.75rem 0.75rem 0.75rem 0;
  padding: 0.75rem;
  max-width: 85%;
  word-break: break-word;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);

  @media (min-width: 768px) {
    max-width: 80%;
  }
}

.message-activity {
  margin-bottom: 0.75rem;
  padding-bottom: 0.75rem;
  border-bottom: 1px solid #404040;
  font-size: 0.75rem;
}

.ai-message-content {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.copy-button {
  align-self: flex-end;
  background-color: #404040;
  border-color: #525252;
  color: #d4d4d4;

  &:hover {
    background-color: #525252;
  }
}

.loading-message {
  display: flex;
  align-items: flex-start;
  gap: 0.75rem;
  margin-top: 0.75rem;
}

.simple-loader {
  display: flex;
  align-items: center;
  justify-content: flex-start;
  height: 100%;
  gap: 0.5rem;
  min-height: 3.5rem;

  span {
    color: #a3a3a3;
  }

  ::ng-deep {
    .mat-mdc-progress-spinner circle {
      stroke: #a3a3a3;
    }
  }
}

.input-area {
  flex-shrink: 0;
}
